# 计算叙事文本Volume特征指南

## 概述

Volume特征衡量叙事文本在语义空间中的"体积"，反映了叙事内容的丰富性和多样性。Volume值越大，说明文本在不同语义维度上的变化越丰富，叙事内容越多样化。

## 核心原理

Volume通过以下步骤计算：
1. 将文本分割成多个块（chunks）
2. 为每个块计算平均词嵌入向量
3. 在高维语义空间中，这些块向量形成一个点云
4. 计算包围这些点的最小体积椭球
5. 椭球的体积即为Volume特征值

## 完整实现步骤

### 步骤1：环境准备

```python
import numpy as np
import nltk
from nltk.stem import WordNetLemmatizer
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from gensim.models.keyedvectors import load_word2vec_format
from scipy.stats.mstats import gmean

# 下载必要的NLTK数据
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('wordnet')
```

### 步骤2：文本预处理

```python
def preprocess_text(document: str, stemmer, en_stop: set) -> str:
    """预处理文本：去除特殊字符、停用词、词形还原"""
    import re
    
    # 去除特殊字符
    document = re.sub(r'\W', ' ', str(document))
    # 去除单个字符
    document = re.sub(r'\s+[a-zA-Z]\s+', ' ', document)
    document = re.sub(r'\^[a-zA-Z]\s+', ' ', document)
    # 合并多个空格
    document = re.sub(r'\s+', ' ', document, flags=re.I)
    # 去除前缀'b'
    document = re.sub(r'^b\s+', '', document)
    # 转换为小写
    document = document.lower()
    
    # 词形还原和停用词过滤
    tokens = document.split()
    tokens = [stemmer.lemmatize(word) for word in tokens]
    tokens = [word for word in tokens if word not in en_stop]
    tokens = [word for word in tokens if len(word) > 3]
    
    return ' '.join(tokens)
```

### 步骤3：文本分块

```python
def get_data_chunks(document: str, T: int = 20) -> list:
    """将文档分割成T个块"""
    tokens = word_tokenize(document)
    chunk_len = len(tokens) / T
    
    chunks = []
    for i in range(T):
        start_idx = int(i * chunk_len)
        end_idx = int((i + 1) * chunk_len)
        if i == T - 1:  # 最后一个块包含剩余所有词
            end_idx = len(tokens)
        chunks.append(tokens[start_idx:end_idx])
    
    return chunks
```

### 步骤4：计算块嵌入向量

```python
def get_chunk_embeddings(ft_model, chunks: list) -> list:
    """计算每个块的平均嵌入向量"""
    avg_embs = []
    for chunk in chunks:
        avg_emb = np.zeros((300,))  # FastText默认300维
        if len(chunk):
            embs = []
            for token in chunk:
                try:
                    # 尝试获取词向量
                    if hasattr(ft_model, 'wv'):
                        embs.append(np.array(ft_model.wv[token]))
                    else:
                        embs.append(np.array(ft_model[token]))
                except KeyError:
                    # 词不在词汇表中，跳过
                    continue
            
            if embs:  # 如果有有效的词向量
                embs = np.stack(embs)
                avg_emb = np.average(embs, axis=0)
        
        avg_embs.append(avg_emb)
    return avg_embs
```

### 步骤5：最小体积椭球算法（Khachiyan算法）

```python
def get_min_vol_ellipse(P, tolerance=0.01):
    """计算包围点集P的最小体积椭球"""
    (N, d) = np.shape(P)
    d = float(d)
    
    # Q是工作数组
    Q = np.vstack([np.copy(P.T), np.ones(N)]) 
    QT = Q.T
    
    # 初始化
    err = 1.0 + tolerance
    u = (1.0 / N) * np.ones(N)
    
    # Khachiyan算法迭代
    while err > tolerance:
        V = np.dot(Q, np.dot(np.diag(u), QT))
        M = np.diag(np.dot(QT, np.dot(np.linalg.inv(V), Q)))
        j = np.argmax(M)
        maximum = M[j]
        step_size = (maximum - d - 1.0) / ((d + 1.0) * (maximum - 1.0))
        new_u = (1.0 - step_size) * u
        new_u[j] += step_size
        err = np.linalg.norm(new_u - u)
        u = new_u
    
    # 椭球中心
    center = np.dot(P.T, u)
    
    # 椭球矩阵A
    A = np.linalg.inv(
        np.dot(P.T, np.dot(np.diag(u), P)) - 
        np.array([[a * b for b in center] for a in center])
    ) / d
    
    return A, center
```

### 步骤6：Volume计算

```python
def get_volume(chunk_emb: list, tolerance: float = 0.01, emb_dim: int = 300) -> float:
    """计算块嵌入向量的Volume特征"""
    P = np.array(chunk_emb)
    
    # 计算矩阵秩
    rank = np.linalg.matrix_rank(P, tolerance)
    
    if rank < emb_dim or (rank == emb_dim and P.shape[0] <= emb_dim):
        # 降维处理
        tempA = P[1:,:].transpose() - P[0,:].transpose().reshape(-1, 1) @ np.ones((1, P.shape[0] - 1))
        U, S, _ = np.linalg.svd(tempA)
        S1 = U[:, :rank-1]
        tempP = np.vstack([(S1.transpose() @ tempA).transpose(), np.zeros((1, rank-1))])
        A, _ = get_min_vol_ellipse(tempP, tolerance)
    else:
        A, _ = get_min_vol_ellipse(P, tolerance)
    
    # SVD分解获取特征值
    U, S, _ = np.linalg.svd(A)
    
    # Volume = 1/几何平均(√特征值)
    return 1 / gmean(np.sqrt(S))
```

### 步骤7：完整的Volume计算函数

```python
def calculate_narrative_volume(text: str, fasttext_model_path: str, T: int = 20) -> float:
    """
    计算叙事文本的Volume特征
    
    Args:
        text: 输入的叙事文本
        fasttext_model_path: FastText模型文件路径
        T: 分块数量，默认20
    
    Returns:
        float: Volume特征值
    """
    # 1. 加载FastText模型
    ft_model = load_word2vec_format(fasttext_model_path)
    
    # 2. 初始化预处理工具
    stemmer = WordNetLemmatizer()
    en_stop = set(stopwords.words('english'))
    
    # 3. 文本预处理
    preprocessed_text = preprocess_text(text, stemmer, en_stop)
    
    # 4. 文本分块
    chunks = get_data_chunks(preprocessed_text, T)
    
    # 5. 计算块嵌入向量
    chunk_embeddings = get_chunk_embeddings(ft_model, chunks)
    
    # 6. 计算Volume
    volume = get_volume(chunk_embeddings)
    
    return volume
```

## 使用示例

```python
# 示例文本
narrative_text = """
This is a sample narrative text about a journey through different landscapes. 
The story begins in a bustling city with tall buildings and busy streets. 
As the protagonist travels, they encounter various challenges and meet interesting characters. 
The narrative explores themes of growth, discovery, and human connection.
Each chapter reveals new aspects of the world and the character's inner journey.
"""

# 计算Volume特征
try:
    volume_score = calculate_narrative_volume(
        text=narrative_text,
        fasttext_model_path="saved/model/fasttext.vec",
        T=20
    )
    print(f"叙事文本的Volume特征值: {volume_score:.4f}")
except Exception as e:
    print(f"计算过程中出现错误: {e}")
```

## 特征解释

- **Volume值范围**: 通常在0.1到10之间，具体范围取决于文本长度和复杂度
- **高Volume值**: 表示叙事内容丰富多样，在语义空间中覆盖更大的区域
- **低Volume值**: 表示叙事内容相对单一，语义变化较小
- **应用场景**: 可用于评估文本的语义丰富度、叙事复杂性等

## 注意事项

1. **模型依赖**: 需要预训练的FastText模型文件
2. **文本长度**: 文本太短可能导致分块不均匀，影响计算结果
3. **语言支持**: 当前实现主要针对英文文本
4. **计算复杂度**: 对于长文本，计算时间可能较长
5. **数值稳定性**: 在某些极端情况下可能出现数值不稳定问题

## 完整可运行Demo

### Demo 1: 基础Volume计算

```python
# volume_calculator_demo.py
import numpy as np
import nltk
from nltk.stem import WordNetLemmatizer
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from scipy.stats.mstats import gmean
import re

# 如果没有下载过NLTK数据，需要先下载
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('wordnet')

class VolumeCalculator:
    def __init__(self, model_path=None):
        """
        初始化Volume计算器

        Args:
            model_path: FastText模型路径，如果为None则使用随机向量（仅用于演示）
        """
        self.stemmer = WordNetLemmatizer()
        self.en_stop = set(stopwords.words('english'))
        self.model_path = model_path
        self.ft_model = None

        if model_path:
            try:
                from gensim.models.keyedvectors import load_word2vec_format
                self.ft_model = load_word2vec_format(model_path)
                print(f"成功加载FastText模型: {model_path}")
            except Exception as e:
                print(f"加载模型失败: {e}")
                print("将使用随机向量进行演示")

    def preprocess_text(self, document: str) -> str:
        """预处理文本"""
        # 去除特殊字符
        document = re.sub(r'\W', ' ', str(document))
        document = re.sub(r'\s+[a-zA-Z]\s+', ' ', document)
        document = re.sub(r'\^[a-zA-Z]\s+', ' ', document)
        document = re.sub(r'\s+', ' ', document, flags=re.I)
        document = re.sub(r'^b\s+', '', document)
        document = document.lower()

        # 词形还原和停用词过滤
        tokens = document.split()
        tokens = [self.stemmer.lemmatize(word) for word in tokens]
        tokens = [word for word in tokens if word not in self.en_stop]
        tokens = [word for word in tokens if len(word) > 3]

        return ' '.join(tokens)

    def get_data_chunks(self, document: str, T: int = 20) -> list:
        """将文档分割成T个块"""
        tokens = word_tokenize(document)
        if len(tokens) < T:
            print(f"警告: 文本长度({len(tokens)}词)小于分块数({T})，调整分块数为{len(tokens)}")
            T = max(1, len(tokens))

        chunk_len = len(tokens) / T
        chunks = []

        for i in range(T):
            start_idx = int(i * chunk_len)
            end_idx = int((i + 1) * chunk_len)
            if i == T - 1:
                end_idx = len(tokens)
            chunks.append(tokens[start_idx:end_idx])

        return chunks

    def get_chunk_embeddings(self, chunks: list) -> list:
        """计算每个块的平均嵌入向量"""
        avg_embs = []

        for chunk in chunks:
            avg_emb = np.zeros((300,))
            if len(chunk):
                embs = []
                for token in chunk:
                    try:
                        if self.ft_model:
                            # 使用真实的FastText模型
                            if hasattr(self.ft_model, 'wv'):
                                embs.append(np.array(self.ft_model.wv[token]))
                            else:
                                embs.append(np.array(self.ft_model[token]))
                        else:
                            # 使用随机向量进行演示
                            np.random.seed(hash(token) % 2**32)
                            embs.append(np.random.normal(0, 1, 300))
                    except KeyError:
                        # 词不在词汇表中，使用随机向量
                        np.random.seed(hash(token) % 2**32)
                        embs.append(np.random.normal(0, 0.1, 300))

                if embs:
                    embs = np.stack(embs)
                    avg_emb = np.average(embs, axis=0)

            avg_embs.append(avg_emb)

        return avg_embs

    def get_min_vol_ellipse(self, P, tolerance=0.01):
        """计算最小体积椭球"""
        P = np.array(P)
        (N, d) = np.shape(P)
        d = float(d)

        if N <= 1:
            return np.eye(d), np.mean(P, axis=0) if N > 0 else np.zeros(d)

        Q = np.vstack([np.copy(P.T), np.ones(N)])
        QT = Q.T

        err = 1.0 + tolerance
        u = (1.0 / N) * np.ones(N)

        max_iterations = 1000
        iteration = 0

        while err > tolerance and iteration < max_iterations:
            try:
                V = np.dot(Q, np.dot(np.diag(u), QT))
                V_inv = np.linalg.inv(V)
                M = np.diag(np.dot(QT, np.dot(V_inv, Q)))
                j = np.argmax(M)
                maximum = M[j]

                if maximum <= d + 1:
                    break

                step_size = (maximum - d - 1.0) / ((d + 1.0) * (maximum - 1.0))
                new_u = (1.0 - step_size) * u
                new_u[j] += step_size
                err = np.linalg.norm(new_u - u)
                u = new_u
                iteration += 1

            except np.linalg.LinAlgError:
                break

        center = np.dot(P.T, u)

        try:
            A = np.linalg.inv(
                np.dot(P.T, np.dot(np.diag(u), P)) -
                np.outer(center, center)
            ) / d
        except np.linalg.LinAlgError:
            A = np.eye(d) * 0.01

        return A, center

    def get_volume(self, chunk_emb: list, tolerance: float = 0.01, emb_dim: int = 300) -> float:
        """计算Volume特征"""
        P = np.array(chunk_emb)

        # 移除全零向量
        P = P[~np.all(P == 0, axis=1)]

        if len(P) < 2:
            return 0.01  # 返回最小值

        rank = np.linalg.matrix_rank(P, tolerance)

        if rank < emb_dim or (rank == emb_dim and P.shape[0] <= emb_dim):
            if P.shape[0] > 1:
                tempA = P[1:,:].transpose() - P[0,:].transpose().reshape(-1, 1) @ np.ones((1, P.shape[0] - 1))
                try:
                    U, S, _ = np.linalg.svd(tempA)
                    if rank > 1:
                        S1 = U[:, :rank-1]
                        tempP = np.vstack([(S1.transpose() @ tempA).transpose(), np.zeros((1, rank-1))])
                        A, _ = self.get_min_vol_ellipse(tempP, tolerance)
                    else:
                        A = np.eye(tempA.shape[0]) * 0.01
                except:
                    A = np.eye(P.shape[1]) * 0.01
            else:
                A = np.eye(P.shape[1]) * 0.01
        else:
            A, _ = self.get_min_vol_ellipse(P, tolerance)

        try:
            U, S, _ = np.linalg.svd(A)
            S = np.maximum(S, 1e-10)  # 避免除零
            return 1 / gmean(np.sqrt(S))
        except:
            return 0.01

    def calculate_volume(self, text: str, T: int = 20) -> dict:
        """
        计算文本的Volume特征

        Returns:
            dict: 包含volume值和中间结果的字典
        """
        print("开始计算Volume特征...")

        # 1. 文本预处理
        print("1. 文本预处理...")
        preprocessed_text = self.preprocess_text(text)
        print(f"   原始文本长度: {len(text)} 字符")
        print(f"   预处理后长度: {len(preprocessed_text)} 字符")

        # 2. 文本分块
        print("2. 文本分块...")
        chunks = self.get_data_chunks(preprocessed_text, T)
        print(f"   分块数量: {len(chunks)}")
        print(f"   平均每块词数: {np.mean([len(chunk) for chunk in chunks]):.1f}")

        # 3. 计算块嵌入向量
        print("3. 计算块嵌入向量...")
        chunk_embeddings = self.get_chunk_embeddings(chunks)
        print(f"   嵌入向量维度: {len(chunk_embeddings)} x {len(chunk_embeddings[0])}")

        # 4. 计算Volume
        print("4. 计算Volume特征...")
        volume = self.get_volume(chunk_embeddings)
        print(f"   Volume值: {volume:.6f}")

        return {
            'volume': volume,
            'num_chunks': len(chunks),
            'avg_chunk_length': np.mean([len(chunk) for chunk in chunks]),
            'preprocessed_text_length': len(preprocessed_text.split()),
            'original_text_length': len(text)
        }

# 演示代码
if __name__ == "__main__":
    # 示例文本
    sample_texts = [
        # 简单叙事
        """
        The cat sat on the mat. It was a sunny day. The cat was happy.
        Birds were singing in the trees. Everything was peaceful and calm.
        """,

        # 复杂叙事
        """
        In the bustling metropolis of New York, Sarah navigated through the crowded streets,
        her mind racing with thoughts of the upcoming presentation. The skyscrapers towered
        above her like silent guardians, their glass facades reflecting the golden sunset.
        As she entered the corporate building, memories of her childhood in rural Montana
        flooded back - the vast open fields, the smell of fresh hay, and her grandmother's
        warm apple pie. The contrast couldn't be more stark. In the elevator, she met
        an elderly gentleman who reminded her of her grandfather. They struck up a
        conversation about life, dreams, and the pursuit of happiness. By the time she
        reached the 40th floor, Sarah had gained a new perspective on her career and
        life choices. The presentation went smoothly, but more importantly, she had
        rediscovered what truly mattered to her.
        """,

        # 重复性文本
        """
        The same thing happened every day. John woke up at seven. John ate breakfast.
        John went to work. John came home. John watched TV. John went to bed.
        The same routine repeated endlessly.
        """
    ]

    # 创建计算器实例
    calculator = VolumeCalculator()  # 不使用真实模型，用随机向量演示

    print("=" * 80)
    print("Volume特征计算演示")
    print("=" * 80)

    for i, text in enumerate(sample_texts, 1):
        print(f"\n【示例 {i}】")
        print("-" * 40)
        print("文本内容:")
        print(text.strip())
        print("-" * 40)

        result = calculator.calculate_volume(text, T=10)  # 使用较少的分块数

        print(f"\n结果摘要:")
        print(f"  Volume值: {result['volume']:.6f}")
        print(f"  分块数量: {result['num_chunks']}")
        print(f"  平均块长度: {result['avg_chunk_length']:.1f} 词")
        print(f"  预处理后文本长度: {result['preprocessed_text_length']} 词")

        print("=" * 80)
```

### Demo 2: 使用真实FastText模型

```python
# volume_with_fasttext_demo.py
# 需要先下载FastText模型文件

def download_fasttext_model():
    """下载FastText模型的辅助函数"""
    import urllib.request
    import zipfile
    import os

    model_dir = "models"
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    model_path = os.path.join(model_dir, "wiki-news-300d-1M.vec")

    if not os.path.exists(model_path):
        print("下载FastText模型...")
        url = "https://dl.fbaipublicfiles.com/fasttext/vectors-english/wiki-news-300d-1M.vec.zip"
        zip_path = os.path.join(model_dir, "wiki-news-300d-1M.vec.zip")

        urllib.request.urlretrieve(url, zip_path)

        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(model_dir)

        os.remove(zip_path)
        print(f"模型已下载到: {model_path}")

    return model_path

# 使用真实模型的示例
if __name__ == "__main__":
    try:
        # 下载模型（如果需要）
        model_path = download_fasttext_model()

        # 使用真实模型创建计算器
        calculator = VolumeCalculator(model_path)

        # 测试文本
        test_text = """
        The journey of a thousand miles begins with a single step. This ancient wisdom
        resonates through the corridors of time, reminding us that every great achievement
        starts with a simple decision to begin. In the realm of artificial intelligence,
        we witness this principle in action as researchers and engineers take incremental
        steps toward creating systems that can understand, learn, and adapt. Each algorithm,
        each dataset, each breakthrough builds upon the foundation laid by previous work,
        creating a magnificent tapestry of human ingenuity and technological progress.
        """

        result = calculator.calculate_volume(test_text, T=15)
        print(f"使用真实FastText模型的Volume值: {result['volume']:.6f}")

    except Exception as e:
        print(f"运行出错: {e}")
        print("请确保网络连接正常，或手动下载FastText模型")
```

## 中文数据集的修改方案

### 修改思路

处理中文文本需要考虑以下几个关键差异：

1. **分词方式**: 中文没有天然的空格分隔，需要使用专门的分词工具
2. **停用词**: 需要使用中文停用词表
3. **词嵌入模型**: 需要使用中文预训练的词向量模型
4. **文本预处理**: 中文的标点符号和特殊字符处理方式不同

### 具体修改步骤

#### 1. 安装中文处理依赖

```bash
pip install jieba
pip install gensim
# 下载中文FastText模型或Word2Vec模型
```

#### 2. 修改后的中文Volume计算器

```python
# chinese_volume_calculator.py
import jieba
import numpy as np
from scipy.stats.mstats import gmean
import re

class ChineseVolumeCalculator:
    def __init__(self, model_path=None):
        """
        中文Volume计算器

        Args:
            model_path: 中文词向量模型路径
        """
        # 中文停用词（简化版本，实际使用时建议使用完整停用词表）
        self.zh_stop = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还', '把', '比', '或者', '因为', '所以'
        }

        self.model_path = model_path
        self.ft_model = None

        if model_path:
            try:
                from gensim.models import KeyedVectors
                self.ft_model = KeyedVectors.load_word2vec_format(model_path)
                print(f"成功加载中文词向量模型: {model_path}")
            except Exception as e:
                print(f"加载模型失败: {e}")
                print("将使用随机向量进行演示")

    def preprocess_chinese_text(self, document: str) -> str:
        """中文文本预处理"""
        # 去除英文字符和数字
        document = re.sub(r'[a-zA-Z0-9]', '', document)

        # 去除标点符号
        document = re.sub(r'[^\u4e00-\u9fff]', ' ', document)

        # 使用jieba分词
        tokens = jieba.lcut(document)

        # 过滤停用词和短词
        tokens = [word for word in tokens if word not in self.zh_stop]
        tokens = [word for word in tokens if len(word) > 1]

        return ' '.join(tokens)

    def get_data_chunks(self, document: str, T: int = 20) -> list:
        """将中文文档分割成T个块"""
        # 使用jieba分词
        tokens = jieba.lcut(document)

        if len(tokens) < T:
            T = max(1, len(tokens))

        chunk_len = len(tokens) / T
        chunks = []

        for i in range(T):
            start_idx = int(i * chunk_len)
            end_idx = int((i + 1) * chunk_len)
            if i == T - 1:
                end_idx = len(tokens)
            chunks.append(tokens[start_idx:end_idx])

        return chunks

    def get_chunk_embeddings(self, chunks: list) -> list:
        """计算中文块嵌入向量"""
        avg_embs = []

        for chunk in chunks:
            avg_emb = np.zeros((300,))  # 假设使用300维向量
            if len(chunk):
                embs = []
                for token in chunk:
                    try:
                        if self.ft_model and token in self.ft_model:
                            embs.append(np.array(self.ft_model[token]))
                        else:
                            # 使用字符hash生成一致的随机向量
                            np.random.seed(hash(token) % 2**32)
                            embs.append(np.random.normal(0, 1, 300))
                    except:
                        np.random.seed(hash(token) % 2**32)
                        embs.append(np.random.normal(0, 0.1, 300))

                if embs:
                    embs = np.stack(embs)
                    avg_emb = np.average(embs, axis=0)

            avg_embs.append(avg_emb)

        return avg_embs

    # 其他方法与英文版本相同
    def get_min_vol_ellipse(self, P, tolerance=0.01):
        """计算最小体积椭球（与英文版本相同）"""
        # ... 代码与英文版本相同
        pass

    def get_volume(self, chunk_emb: list, tolerance: float = 0.01, emb_dim: int = 300) -> float:
        """计算Volume特征（与英文版本相同）"""
        # ... 代码与英文版本相同
        pass

    def calculate_volume(self, text: str, T: int = 20) -> dict:
        """计算中文文本的Volume特征"""
        print("开始计算中文文本Volume特征...")

        # 1. 中文文本预处理
        print("1. 中文文本预处理...")
        preprocessed_text = self.preprocess_chinese_text(text)
        print(f"   原始文本长度: {len(text)} 字符")
        print(f"   预处理后长度: {len(preprocessed_text)} 字符")

        # 2. 文本分块
        print("2. 文本分块...")
        chunks = self.get_data_chunks(preprocessed_text, T)
        print(f"   分块数量: {len(chunks)}")
        print(f"   平均每块词数: {np.mean([len(chunk) for chunk in chunks]):.1f}")

        # 3. 计算块嵌入向量
        print("3. 计算块嵌入向量...")
        chunk_embeddings = self.get_chunk_embeddings(chunks)

        # 4. 计算Volume
        print("4. 计算Volume特征...")
        volume = self.get_volume(chunk_embeddings)

        return {
            'volume': volume,
            'num_chunks': len(chunks),
            'avg_chunk_length': np.mean([len(chunk) for chunk in chunks]),
            'preprocessed_text_length': len(preprocessed_text.split()),
            'original_text_length': len(text)
        }

# 中文演示
if __name__ == "__main__":
    chinese_texts = [
        """
        在一个遥远的古代王国里，住着一位善良的公主。她每天都会到花园里照料花朵，
        与小鸟们交谈。有一天，一个邪恶的巫师诅咒了整个王国，所有的花朵都枯萎了。
        公主决定踏上寻找解除诅咒方法的旅程。
        """,

        """
        人工智能技术的发展日新月异，从最初的专家系统到现在的深度学习，
        每一次技术革新都带来了巨大的变化。机器学习算法能够从大量数据中
        学习模式，自然语言处理技术让计算机理解人类语言，计算机视觉让
        机器能够"看见"世界。这些技术的融合正在改变我们的生活方式，
        从智能手机的语音助手到自动驾驶汽车，从医疗诊断到金融分析，
        人工智能正在各个领域发挥着重要作用。
        """
    ]

    calculator = ChineseVolumeCalculator()

    for i, text in enumerate(chinese_texts, 1):
        print(f"\n【中文示例 {i}】")
        print("-" * 40)
        print("文本内容:")
        print(text.strip())
        print("-" * 40)

        result = calculator.calculate_volume(text, T=10)

        print(f"\n结果摘要:")
        print(f"  Volume值: {result['volume']:.6f}")
        print(f"  分块数量: {result['num_chunks']}")
        print(f"  平均块长度: {result['avg_chunk_length']:.1f} 词")

        print("=" * 60)
```

### 中文词向量模型推荐

1. **腾讯AI Lab中文词向量**:
   - 下载地址: https://ai.tencent.com/ailab/nlp/en/embedding.html
   - 包含800万中文词汇的200维向量

2. **中文FastText模型**:
   - 下载地址: https://fasttext.cc/docs/en/crawl-vectors.html
   - 选择中文(zh)版本

3. **Word2Vec中文模型**:
   - 可以使用gensim训练自己的中文语料
   - 或下载预训练的中文Word2Vec模型

### 关键修改点总结

1. **分词**: `jieba.lcut()` 替代 `word_tokenize()`
2. **停用词**: 使用中文停用词表
3. **预处理**: 保留中文字符，去除英文和数字
4. **模型**: 使用中文预训练词向量模型
5. **编码**: 确保文本编码为UTF-8

这样修改后，就可以处理中文叙事文本并计算其Volume特征了。
