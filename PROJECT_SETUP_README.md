# 📋 项目设置和依赖清单

## 概述

本文档详细说明了 `quanNarrative` 项目运行所需的所有文件、目录结构和外部依赖。该项目用于分析学术摘要的叙事形状特征（速度、体积、迂回性）及其对成功的预测能力。

## 📚 文件来源和用途说明

### dataset.json 是什么？
这是**AMiner学术引用数据集**的重命名版本，包含约300万篇学术论文的元数据。每行是一个JSON对象，包含论文摘要、引用次数、发表年份等信息。项目通过分析摘要文本的叙事结构来预测论文的成功程度（以引用次数衡量）。

### chunk_embs.txt 是什么？
这是**文档块嵌入缓存文件**，存储了预计算的文档块向量表示。项目将每篇论文摘要分割成20个块，然后用FastText模型计算每个块的词嵌入向量平均值。这个文件避免了每次运行时重复计算这些向量（计算过程需要数小时）。

## 🔴 必需文件（项目运行必备）

### 1. AMiner学术引用数据集
- **文件名**: `dataset.json` (重命名自 `dblp-ref-0.json`)
- **目标路径**: `c:\Users\<USER>\Desktop\quanNarrative\saved\data\dataset.json`
- **文件描述**: AMiner学术引用数据集，JSON Lines格式（每行一个JSON对象）
- **数据内容**:
  ```json
  {"abstract": "论文摘要文本...", "n_citation": 42, "year": 2020, "venue": "ICML", ...}
  {"abstract": "另一篇论文摘要...", "n_citation": 15, "year": 2019, "venue": "NeurIPS", ...}
  ```
- **字段说明**:
  - `abstract`: 论文摘要文本（项目分析的核心数据）
  - `n_citation`: 论文被引用次数（成功指标）
  - `year`: 发表年份（控制变量）
  - `venue`: 发表会议/期刊（控制变量）
- **数据规模**: 约300万篇学术论文的元数据
- **获取方式**: 
  ```bash
  # 方法1: 直接下载
  wget https://www.kaggle.com/kmader/aminer-academic-citation-dataset/download/o0mFH8IcsQHZEJ2HX1E1%2Fversions%2FzOZutSMcvhpIpY7AXXtt%2Ffiles%2Fdblp-ref-0.json?datasetVersionNumber=2
  
  # 方法2: 使用kagglehub (推荐)
  pip install kagglehub
  python -c "import kagglehub; path = kagglehub.dataset_download('kmader/aminer-academic-citation-dataset'); print('数据集路径:', path)"
  ```
- **重要性**: ⭐⭐⭐⭐⭐ **极高** - 项目核心数据源，无此文件无法运行

### 2. FastText预训练模型
- **文件名**: `fasttext.vec` (重命名自 `wiki-news-300d-1M.vec`)
- **目标路径**: `c:\Users\<USER>\Desktop\quanNarrative\saved\model\fasttext.vec`
- **文件描述**: Facebook预训练的300维FastText词向量模型（约2.5GB）
- **获取方式**: 
  ```bash
  # 创建目录
  mkdir fasttext_model
  cd fasttext_model
  
  # 下载并解压
  wget https://dl.fbaipublicfiles.com/fasttext/vectors-english/wiki-news-300d-1M.vec.zip
  unzip wiki-news-300d-1M.vec.zip
  rm wiki-news-300d-1M.vec.zip  # 删除压缩包节省空间
  cd ..
  ```
- **重要性**: ⭐⭐⭐⭐⭐ **极高** - 词嵌入模型，项目核心功能依赖

## 🟡 需要创建的目录结构

```
c:\Users\<USER>\Desktop\quanNarrative\
├── saved\                         # 主要工作目录（已存在）
│   ├── data\                      # 数据目录（已存在但为空）
│   │   ├── dataset.json          # AMiner数据集（需要下载）
│   │   └── chunk_embs.txt         # 块嵌入缓存（运行时生成）
│   └── model\                     # 模型目录（需要创建）
│       └── fasttext.vec           # 预训练词向量（需要下载）
├── word_embedding_measures\       # 备用项目目录（如果使用默认参数）
│   └── data\                      # 备用数据目录
└── utils\                         # 工具模块（已存在）
```

### 创建目录命令
```bash
# Windows PowerShell - 只需创建 saved/model 目录
mkdir saved\model

# 如果使用默认参数，还需要创建：
mkdir word_embedding_measures\data
```

## 🟢 运行时生成文件（无需手动下载）

### 3. 块嵌入缓存文件
- **文件名**: `chunk_embs.txt`
- **目标路径**: `c:\Users\<USER>\Desktop\quanNarrative\saved\data\chunk_embs.txt`
- **文件描述**: 文档块嵌入向量的缓存文件，CSV格式的数值矩阵
- **数据内容**:
  ```
  # 第一行: 形状信息 (文档数, 块数, 嵌入维度)
  # 30000,20,300
  0.1234,-0.5678,0.9012,...  # 第1个文档第1个块的300维嵌入向量
  0.2345,-0.6789,0.0123,...  # 第1个文档第2个块的300维嵌入向量
  ...
  ```
- **生成过程**:
  1. 将每个论文摘要分割成T个块（默认20个块）
  2. 对每个块的词汇计算FastText嵌入向量
  3. 对每个块内的词向量求平均，得到块嵌入向量
  4. 保存为CSV格式避免重复计算（耗时数小时）
- **数据规模**: 30000篇论文 × 20个块 × 300维 = 1.8亿个数值
- **获取方式**: 首次运行时自动生成，后续运行直接加载
- **重要性**: ⭐⭐⭐ **中等** - 性能优化文件，可重新生成但耗时较长

## 🔵 外部依赖包

### 4. Python包依赖
确保已安装 `requirements.txt` 中的所有包：
```bash
pip install -r requirements.txt
```

包含的主要依赖：
- `numpy` - 数值计算
- `scipy>=1.6.2` - 科学计算
- `sklearn==0.24.1` - 机器学习
- `nltk==3.6.1` - 自然语言处理
- `gensim==4.1.2` - 词嵌入模型
- `matplotlib` - 数据可视化

### 5. NLTK数据包
- **描述**: 自然语言处理工具包的数据文件
- **获取方式**: 
  ```python
  import nltk
  nltk.download('punkt')      # 分词器
  nltk.download('stopwords')  # 停用词
  nltk.download('wordnet')    # 词网数据库
  ```
- **重要性**: ⭐⭐⭐⭐ **高** - 文本预处理必需

## 🟠 可选文件（特定功能使用）

### 6. 16k说服力数据集
- **目标路径**: `c:\Users\<USER>\Desktop\persuasive_classifier\16k_persuasiveness\data\UKPConvArg1Strict-XML\`
- **文件描述**: XML格式的说服力配对数据集，用于特定的说服力分析功能
- **获取方式**: 需要从UKP ConvArg1数据集获取（项目特定功能，一般用户可忽略）
- **重要性**: ⭐⭐ **低** - 仅用于utils/data.py中的get_persuasive_pairs_xml函数

## 📊 下载优先级排序

1. **🔴 优先级1（必须）**:
   - AMiner数据集 (重命名为 `dataset.json`)
   - FastText模型 (重命名为 `fasttext.vec`)

2. **🟡 优先级2（重要）**: 
   - 创建目录结构
   - 安装Python依赖
   - 下载NLTK数据包

3. **🟢 优先级3（可选）**: 
   - 16k说服力数据集

## 🚀 快速设置脚本

### Windows PowerShell 完整设置脚本
```powershell
# 1. 创建目录结构
Write-Host "创建目录结构..." -ForegroundColor Green
mkdir saved\model, word_embedding_measures\data -Force

# 2. 安装Python依赖
Write-Host "安装Python依赖..." -ForegroundColor Green
pip install -r requirements.txt
pip install kagglehub

# 3. 下载NLTK数据
Write-Host "下载NLTK数据..." -ForegroundColor Green
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"

# 4. 下载FastText模型
Write-Host "下载FastText模型..." -ForegroundColor Green
cd saved\model
Invoke-WebRequest -Uri "https://dl.fbaipublicfiles.com/fasttext/vectors-english/wiki-news-300d-1M.vec.zip" -OutFile "wiki-news-300d-1M.vec.zip"
Expand-Archive -Path "wiki-news-300d-1M.vec.zip" -DestinationPath "."
Rename-Item "wiki-news-300d-1M.vec" "fasttext.vec"
Remove-Item "wiki-news-300d-1M.vec.zip"
cd ..\..

# 5. 下载AMiner数据集
Write-Host "下载AMiner数据集..." -ForegroundColor Green
python -c "import kagglehub; import shutil; path = kagglehub.dataset_download('kmader/aminer-academic-citation-dataset'); shutil.copy(path + '/dblp-ref-0.json', 'saved/data/dataset.json'); print('数据集已复制到 saved/data/dataset.json')"

Write-Host "设置完成！" -ForegroundColor Green
Write-Host "现在可以运行: python main.py --proj_dir saved/ --model_name model/fasttext.vec --data_file data/dataset.json --train_model" -ForegroundColor Yellow
```

## 📝 验证设置

运行以下命令验证所有文件是否正确设置：

```python
import os

# 检查必需文件
required_files = [
    'saved/data/dataset.json',
    'saved/model/fasttext.vec'
]

for file_path in required_files:
    if os.path.exists(file_path):
        size = os.path.getsize(file_path) / (1024*1024)  # MB
        print(f"✅ {file_path} 存在 ({size:.1f} MB)")
    else:
        print(f"❌ {file_path} 缺失")

# 检查目录结构
required_dirs = [
    'saved/data', 'saved/model', 'word_embedding_measures/data'
]

for dir_path in required_dirs:
    if os.path.exists(dir_path):
        print(f"✅ 目录 {dir_path} 存在")
    else:
        print(f"❌ 目录 {dir_path} 缺失")
```

## 🔧 故障排除

### 常见问题

1. **下载速度慢**: FastText模型文件较大（2.5GB），建议使用稳定的网络连接
2. **磁盘空间**: 确保至少有5GB可用空间（数据集+模型+缓存文件）
3. **权限问题**: 在Windows上可能需要管理员权限创建某些目录
4. **Kaggle API**: 如果使用kagglehub遇到问题，可能需要配置Kaggle API凭据

### 手动下载备选方案

如果自动下载失败，可以手动下载：

1. 访问 [Kaggle AMiner数据集页面](https://www.kaggle.com/kmader/aminer-academic-citation-dataset)
2. 下载 `dblp-ref-0.json` 并重命名为 `dataset.json`，放入 `saved/data/` 目录
3. 访问 [FastText官网](https://fasttext.cc/docs/en/english-vectors.html)
4. 下载 `wiki-news-300d-1M.vec.zip`，解压后重命名为 `fasttext.vec`，放入 `saved/model/` 目录

## 📚 相关文档

- [原始README.md](README.md) - 项目基本信息和使用说明
- [Toubia等人论文](Toubia%20等%20-%202021%20-%20How%20quantifying%20the%20shape%20of%20stories%20predicts%20their%20success.pdf) - 理论背景
- [word_embedding_notebook.ipynb](word_embedding_notebook.ipynb) - 交互式示例

---

**注意**: 完成以上设置后，项目应该可以正常运行。首次运行建议使用：
```bash
python main.py --proj_dir saved/ --model_name model/fasttext.vec --data_file data/dataset.json --train_model
```
